"use server";
import React from "react";
import Faq from "~/telexComponents/homePageFaqs";
import Hero from "../components/single-product/Hero";
import ApplicationTools from "../components/single-product/ApplicationTools";
import CaseStudy from "../components/single-product/CaseStudies";
import ImageSection from "../components/single-product/ImageSection";
import MonitorComponent from "../components/single-product/Monitor";
import Guides from "../components/single-product/Guides";
import KeepTrack from "../components/single-product/KeepTrack";
import ImageBg from "../_assets/push-notification-bg.svg";
import CaseOne from "../_assets/push-case-one.svg";
import CaseTwo from "../_assets/push-case-two.svg";
import { pushnotificationtechnologies } from "../data/push-notification-technologies";
import { pushNotificationFaq } from "../data/push-notification-faqs";

const firstData = [
  {
    id: 1,
    title: "Notification Delivery Tracking",
    content: `These are automated alerts sent in response to events or actions taken by the user. Order confirmations, shipment updates, and password reset alerts are a few examples. They play a critical role in delivering the vital, timely information that consumers demand.`,
  },
  {
    id: 2,
    title: "Use Telex  for  Promotional Push Notifications",
    content: `These alerts are geared toward marketing and are frequently used to advertise new products, deals, or discounts. To get customers back to the app or website, an e-commerce platform may, for example, send a push notice announcing a flash sale or exclusive seasonal discount.`,
  },
  {
    id: 3,
    title: "Use Telex  for  Informational Push Notifications",
    content: `The main purpose of these notifications is to send out alerts, updates, or reminders. Depending on the app's focus, these could include event reminders, sports scores, and breaking news notifications.`,
  },
  {
    id: 4,
    title: "Use Telex  for  Mobile Push alerts",
    content: `Specifically, through platforms like iOS and Android, these alerts are sent to mobile devices. Because mobile push notifications show up directly on users' smartphone screens and are difficult to ignore, they are great at grabbing users' attention.`,
  },
];

const casestudyData = [
  {
    id: 1,
    title: "How Telex can deliver Increased User Engagement",
    image: CaseOne?.src,
    // By delivering important and timely updates, push notifications help maintain users' attention.
    content: `
    Push notifications provide users with interesting content in real-time that makes them stay connected, whether it's a warning about an event or the latest breaking news.`,
  },
  {
    id: 2,
    title: "Mobile App Notifications",
    image: CaseTwo.src,
    content: `Ensure push notifications are reliably delivered for apps, enhancing user engagement.`,
  },
  {
    id: 3,
    title: "E-commerce Alerts",
    image: CaseOne?.src,
    content: `Monitor critical push notifications for order updates, promotions, and customer communications.`,
  },
  {
    id: 4,
    title: "CRM Integrations",
    image: CaseTwo.src,
    content: `Test and monitor webhooks for customer relationship management systems, ensuring data is sent and received correctly.`,
  },
];

const Page = () => {
  return (
    <>
      <Hero
        breadCumbs="Push Notification"
        title="Guarantee Instant Delivery with Reliable {{Push Notification Monitoring}}"
        content="Centralize and analyze your application and system logs to gain valuable insights into performance, security, and troubleshooting. With real-time log tracking and alerting, you can quickly detect and resolve issues across your entire infrastructure."
        routeName="Sign up"
        learnMoreName="Learn more"
        routeLink="#"
        learnMoreLink="#"
      />
      <ImageSection image={ImageBg} />
      <ApplicationTools
        heading="Push Notification Monitoring Tool designed for you."
        items={firstData}
      />
      <CaseStudy
        tag="Case Studies"
        subheading={`Teams today rely on over 12 monitoring tools on average to gather and analyze the vast amounts of data generated across modern hybrid and server environments. Managing such a wide range of tools can strain your entire organization.`}
        items={casestudyData}
      />
      <MonitorComponent
        heading="Push notifications seamlessly, regardless of the technology."
        items={pushnotificationtechnologies}
      />
      <Guides />
      <Faq faq={pushNotificationFaq} />
      <KeepTrack
        title="Monitor Your Push Notifications with Confidence"
        content="Ensure every push notification reaches its target. Get started with Telex now!"
      />
    </>
  );
};

export default Page;
