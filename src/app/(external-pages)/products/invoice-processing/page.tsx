"use server";
import React from "react";
import Faq from "~/telexComponents/homePageFaqs";
import Hero from "../components/single-product/Hero";
import ApplicationTools from "../components/single-product/ApplicationTools";
import CaseStudy from "../components/single-product/CaseStudies";
import ImageSection from "../components/single-product/ImageSection";
import MonitorComponent from "../components/single-product/Monitor";
import Guides from "../components/single-product/Guides";
import KeepTrack from "../components/single-product/KeepTrack";
import AppPerformanceBg from "../_assets/app-performance-bg.svg";
import CaseOne from "../_assets/case-image-one.svg";
import CaseTwo from "../_assets/case-image-two.svg";
import { technologies } from "../data/technologies";

const firstData = [
  {
    id: 1,
    title: "Automated Invoice Capture",
    content: `Extract data from invoices automatically using advanced OCR technology. Process multiple formats including PDF, images, and digital invoices with high accuracy.`,
  },
  {
    id: 2,
    title: "Smart Data Extraction",
    content: `Intelligently extract key information like invoice numbers, dates, amounts, and line items. Reduce manual data entry and improve accuracy with AI-powered recognition.`,
  },
  {
    id: 3,
    title: "Validation & Verification",
    content: `Automatically validate extracted data against your business rules. Detect duplicates, verify calculations, and ensure compliance with your accounting standards.`,
  },
  {
    id: 4,
    title: "Automated Approval Workflows",
    content: `Streamline invoice approval processes with customizable workflows. Route invoices automatically to the right approvers and track their status in real-time.`,
  },
];

const casestudyData = [
  {
    id: 1,
    title: "How Telex revolutionized invoice processing for enterprise",
    image: CaseOne?.src,
    content: `A global manufacturing company reduced invoice processing time by 85% using Telex's automated invoice processing system.`,
  },
  {
    id: 2,
    title: "Accounts payable transformation",
    image: CaseTwo.src,
    content: `See how a retail chain automated their AP department and saved 40 hours per week in manual processing with Telex.`,
  },
  {
    id: 3,
    title: "Streamlining financial operations",
    image: CaseOne.src,
    content: `Learn how a mid-sized business eliminated data entry errors and reduced processing costs by 60% with Telex's invoice automation.`,
  },
  {
    id: 4,
    title: "Invoice processing efficiency",
    image: CaseTwo?.src,
    content: `Discover how a healthcare provider processed 10,000+ monthly invoices automatically while maintaining 99.9% accuracy.`,
  },
];

const InvoiceProcessing = () => {
  return (
    <>
      <Hero
        breadCumbs="Invoice Processing"
        title="Intelligent {{Invoice Processing}} System"
        content="Transform your invoice processing with our AI-powered automation platform. Capture, extract, and process invoices automatically while ensuring accuracy and compliance."
        routeName="Sign up"
        learnMoreName="Learn more"
        routeLink="/"
        learnMoreLink="#"
      />
      <ImageSection image={AppPerformanceBg} />
      <ApplicationTools
        heading="Invoice Processing Solutions designed for modern businesses."
        items={firstData}
      />
      <CaseStudy
        tag="Success Stories"
        subheading={`Organizations using Telex invoice processing solutions have achieved significant cost savings and efficiency gains. See how our platform can transform your accounts payable operations.`}
        items={casestudyData}
      />
      <MonitorComponent
        heading="Seamless integration with your accounting tools"
        items={technologies}
      />
      <Guides />
      <Faq />
      <KeepTrack
        title="Transform your invoice processing with Telex"
        content="Modernize your accounts payable operations with our intelligent platform. Experience the power of automated invoice processing, faster approvals, and accurate data extraction. Try Telex today."
      />
    </>
  );
};

export default InvoiceProcessing;
