"use server";
import React from "react";
import Faq from "~/telexComponents/homePageFaqs";
import Hero from "../components/single-product/Hero";
import ApplicationTools from "../components/single-product/ApplicationTools";
import CaseStudy from "../components/single-product/CaseStudies";
import ImageSection from "../components/single-product/ImageSection";
import MonitorComponent from "../components/single-product/Monitor";
import Guides from "../components/single-product/Guides";
import KeepTrack from "../components/single-product/KeepTrack";
import AppPerformanceBg from "../_assets/app-performance-bg.svg";
import CaseOne from "../_assets/case-image-one.svg";
import CaseTwo from "../_assets/case-image-two.svg";
import { technologies } from "../data/technologies";

const firstData = [
  {
    id: 1,
    title: "Real-time Threat Detection",
    content: `Protect your website with advanced threat detection powered by AI. Identify and block malicious activities, attacks, and vulnerabilities in real-time.`,
  },
  {
    id: 2,
    title: "Automated Security Scanning",
    content: `Continuously scan your website for vulnerabilities, malware, and security gaps with our automated security assessment tools.`,
  },
  {
    id: 3,
    title: "DDoS Protection",
    content: `Shield your website from distributed denial-of-service attacks with our robust protection system and traffic filtering.`,
  },
  {
    id: 4,
    title: "SSL/TLS Management",
    content: `Automate SSL certificate management and ensure encrypted connections between your users and website at all times.`,
  },
];

const casestudyData = [
  {
    id: 1,
    title: "How Telex Security prevented a major data breach",
    image: CaseOne?.src,
    content: `An e-commerce platform strengthened their security posture with Telex, preventing potential data breaches and maintaining customer trust.`,
  },
  {
    id: 2,
    title: "DDoS protection success story",
    image: CaseTwo.src,
    content: `Learn how a financial services company maintained 99.99% uptime during multiple DDoS attacks using our security system.`,
  },
  {
    id: 3,
    title: "Transforming website security",
    image: CaseOne.src,
    content: `Discover how a healthcare provider achieved HIPAA compliance and protected sensitive patient data with our security solutions.`,
  },
  {
    id: 4,
    title: "Enterprise-scale security implementation",
    image: CaseTwo?.src,
    content: `See how a global retail chain secured their multi-regional websites and protected millions of customer transactions.`,
  },
];

const SiteSecurity = () => {
  return (
    <>
      <Hero
        breadCumbs="Website Security"
        title="Advanced {{Website Security}} Solutions"
        content="Protect your website and user data with enterprise-grade security solutions. Deploy real-time threat detection, automated scanning, and robust DDoS protection to ensure your digital assets remain secure."
        routeName="Sign up"
        learnMoreName="Learn more"
        routeLink="/"
        learnMoreLink="#"
      />
      <ImageSection image={AppPerformanceBg} />
      <ApplicationTools
        heading="Website Security Solutions designed for modern businesses."
        items={firstData}
      />
      <CaseStudy
        tag="Success Stories"
        subheading={`Organizations trust Telex security solutions to protect their digital assets and maintain business continuity. See how our comprehensive security measures can safeguard your online presence.`}
        items={casestudyData}
      />
      <MonitorComponent
        heading="Complete security coverage across all platforms"
        items={technologies}
      />
      <Guides />
      <Faq />
      <KeepTrack
        title="Strengthen your website security with Telex protection"
        content="Implement robust security measures, real-time monitoring, and automated threat response. Try Telex today and ensure your website stays protected against evolving cyber threats."
      />
    </>
  );
};

export default SiteSecurity;
