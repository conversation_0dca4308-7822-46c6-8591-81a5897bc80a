"use server";
import React from "react";
import Faq from "~/telexComponents/homePageFaqs";
import Hero from "../components/single-product/Hero";
import ApplicationTools from "../components/single-product/ApplicationTools";
import CaseStudy from "../components/single-product/CaseStudies";
import ImageSection from "../components/single-product/ImageSection";
import MonitorComponent from "../components/single-product/Monitor";
import Guides from "../components/single-product/Guides";
import KeepTrack from "../components/single-product/KeepTrack";
import ImageBg from "../_assets/log-monitoring-bg.svg";
import CaseOne from "../_assets/log-case-one.svg";
import CaseTwo from "../_assets/log-case-two.svg";
import { logtechnologies } from "../data/log-technologies";
import { logMonitoringFaq } from "../data/log-monitoring-faqs";

const firstData = [
  {
    id: 1,
    title: "Centralized Log Aggregation Monitoring",
    content: `Collect logs from multiple sources into a unified platform for easy access and analysis.`,
  },
  {
    id: 2,
    title: "Log Filtering and Querying",
    content: `Search through logs quickly and filter for specific events or patterns to identify issues.`,
  },
  {
    id: 3,
    title: "Monitor Anomaly Detection",
    content: `Use AI-powered log analysis to detect anomalies that might indicate security threats or system failures.`,
  },
  {
    id: 4,
    title: "Get Real-Time Log Alerts",
    content: `Set up custom alerts based on specific log events, helping you detect and resolve issues immediately.`,
  },
];

const casestudyData = [
  {
    id: 1,
    title: "Application Log Monitoring",
    image: CaseOne?.src,
    content: `Track logs from your applications to detect crashes or performance issues in real-time.`,
  },
  {
    id: 2,
    title: "System Event Tracking",
    image: CaseTwo.src,
    content: `Monitor operating system logs for key events like unauthorized access attempts or critical failures.`,
  },
  {
    id: 3,
    title: "Security Log Monitoring",
    image: CaseOne?.src,
    content: `Analyze security logs to detect suspicious activities, protecting your infrastructure from threats.`,
  },
  {
    id: 4,
    title: "System Event Tracking",
    image: CaseTwo.src,
    content: `Monitor operating system logs for key events like unauthorized access attempts or critical failures.`,
  },
];

const Page = () => {
  return (
    <>
      <Hero
        breadCumbs="Log Monitoring"
        title="Gain Actionable Insights with Unified {{Log Monitoring}}"
        content="Centralize and analyze your application and system logs to gain valuable insights into performance, security, and troubleshooting. With real-time log tracking and alerting, you can quickly detect and resolve issues across your entire infrastructure."
        routeName="Sign up"
        learnMoreName="Learn more"
        routeLink="#"
        learnMoreLink="#"
      />
      <ImageSection image={ImageBg} />
      <ApplicationTools
        heading="Why Log Monitoring Matters"
        items={firstData}
      />
      <CaseStudy
        tag="Case Studies"
        subheading={`Teams today rely on over 12 monitoring tools on average to gather and analyze the vast amounts of data generated across modern hybrid and server environments. Managing such a wide range of tools can strain your entire organization.`}
        items={casestudyData}
      />
      <MonitorComponent
        heading="Monitor your application logs seamlessly, regardless of the technology."
        items={logtechnologies}
      />
      <Guides />
      <Faq faq={logMonitoringFaq} />
      <KeepTrack
        title="Get Full Control of Your Logs with Telex"
        content="Sign up today and experience the power of real-time log monitoring with Telex."
      />
    </>
  );
};

export default Page;
