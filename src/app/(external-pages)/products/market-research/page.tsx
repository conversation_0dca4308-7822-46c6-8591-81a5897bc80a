"use server";
import React from "react";
import Faq from "~/telexComponents/homePageFaqs";
import Hero from "../components/single-product/Hero";
import ApplicationTools from "../components/single-product/ApplicationTools";
import CaseStudy from "../components/single-product/CaseStudies";
import ImageSection from "../components/single-product/ImageSection";
import MonitorComponent from "../components/single-product/Monitor";
import Guides from "../components/single-product/Guides";
import KeepTrack from "../components/single-product/KeepTrack";
import AppPerformanceBg from "../_assets/app-performance-bg.svg";
import CaseOne from "../_assets/case-image-one.svg";
import CaseTwo from "../_assets/case-image-two.svg";
import { technologies } from "../data/technologies";

const firstData = [
  {
    id: 1,
    title: "Advanced Market Analysis",
    content: `Leverage AI-powered analytics to understand market trends, consumer behavior, and competitive landscapes. Make data-driven decisions with comprehensive insights.`,
  },
  {
    id: 2,
    title: "Consumer Insights Dashboard",
    content: `Access real-time consumer data and behavioral analytics. Track preferences, purchasing patterns, and market segments for targeted strategies.`,
  },
  {
    id: 3,
    title: "Competitive Intelligence",
    content: `Monitor competitor activities, market positioning, and industry trends. Stay ahead with automated competitor tracking and analysis.`,
  },
  {
    id: 4,
    title: "Predictive Analytics",
    content: `Forecast market trends and consumer behavior with AI-powered predictive models. Make informed decisions based on data-driven projections.`,
  },
];

const casestudyData = [
  {
    id: 1,
    title: "How Telex transformed market research for retail giants",
    image: CaseOne?.src,
    content: `A major retail chain increased market share by 35% using Telex's advanced market analysis and consumer insight tools.`,
  },
  {
    id: 2,
    title: "Data-driven market expansion",
    image: CaseTwo.src,
    content: `Discover how a global brand successfully entered new markets using Telex's predictive analytics and market intelligence.`,
  },
  {
    id: 3,
    title: "Consumer behavior insights",
    image: CaseOne.src,
    content: `See how a consumer goods company optimized product launches with Telex's consumer behavior analysis platform.`,
  },
  {
    id: 4,
    title: "Strategic market positioning",
    image: CaseTwo?.src,
    content: `Learn how a startup gained competitive advantage using Telex's market research and competitive intelligence tools.`,
  },
];

const MarketResearch = () => {
  return (
    <>
      <Hero
        breadCumbs="Market Research"
        title="Advanced {{Market Research}} Platform"
        content="Transform your market research with our AI-powered platform. Get comprehensive market insights, analyze consumer behavior, and make data-driven decisions - all in one place."
        routeName="Sign up"
        learnMoreName="Learn more"
        routeLink="/"
        learnMoreLink="#"
      />
      <ImageSection image={AppPerformanceBg} />
      <ApplicationTools
        heading="Market Research Tools designed for modern businesses."
        items={firstData}
      />
      <CaseStudy
        tag="Success Stories"
        subheading={`See how organizations are transforming their market research process with Telex. Our platform helps businesses understand markets better and make informed decisions faster.`}
        items={casestudyData}
      />
      <MonitorComponent
        heading="Seamless integration with your favorite tools"
        items={technologies}
      />
      <Guides />
      <Faq />
      <KeepTrack
        title="Elevate your market research with Telex"
        content="Join the future of market research with our AI-powered platform. Experience deeper market insights, better consumer understanding, and data-driven decision making. Try Telex today."
      />
    </>
  );
};

export default MarketResearch;
