"use server";
import React from "react";
import Faq from "~/telexComponents/homePageFaqs";
import Hero from "../components/single-product/Hero";
import ApplicationTools from "../components/single-product/ApplicationTools";
import CaseStudy from "../components/single-product/CaseStudies";
import ImageSection from "../components/single-product/ImageSection";
import MonitorComponent from "../components/single-product/Monitor";
import Guides from "../components/single-product/Guides";
import KeepTrack from "../components/single-product/KeepTrack";
import AppPerformanceBg from "../_assets/app-performance-bg.svg";
import CaseOne from "../_assets/case-image-one.svg";
import CaseTwo from "../_assets/case-image-two.svg";
import { technologies } from "../data/technologies";

const firstData = [
  {
    id: 1,
    title: "Smart Document Creation",
    content: `Create professional documents effortlessly with intelligent templates and automated formatting. Streamline your document workflow with advanced tools.`,
  },
  {
    id: 2,
    title: "Centralized Document Management",
    content: `Keep all your documents organized in one secure location. Access, share, and collaborate on documents from anywhere, at any time.`,
  },
  {
    id: 3,
    title: "Version Control & History",
    content: `Track document changes, manage versions, and maintain complete revision history. Never lose important document updates or modifications.`,
  },
  {
    id: 4,
    title: "Automated Document Workflows",
    content: `Streamline document processes with automated approval workflows, notifications, and task management features.`,
  },
];

const casestudyData = [
  {
    id: 1,
    title: "How Telex streamlined document management for enterprise",
    image: CaseOne?.src,
    content: `A multinational corporation improved document workflow efficiency by 70% using Telex's document management system.`,
  },
  {
    id: 2,
    title: "Digital transformation success story",
    image: CaseTwo.src,
    content: `Discover how a legal firm digitized their document processes and reduced document processing time by 60% with Telex.`,
  },
  {
    id: 3,
    title: "Revolutionizing document collaboration",
    image: CaseOne.src,
    content: `See how a remote-first company enhanced team collaboration and document security using Telex's management solutions.`,
  },
  {
    id: 4,
    title: "Efficient document workflow automation",
    image: CaseTwo?.src,
    content: `Learn how a healthcare provider automated their document workflows and improved compliance management with Telex.`,
  },
];

const DocumentManagement = () => {
  return (
    <>
      <Hero
        breadCumbs="Document Creation & Management"
        title="Intelligent {{Document Management}} System"
        content="Streamline your document creation and management processes with our powerful platform. Create, organize, and collaborate on documents efficiently while maintaining security and compliance."
        routeName="Sign up"
        learnMoreName="Learn more"
        routeLink="/"
        learnMoreLink="#"
      />
      <ImageSection image={AppPerformanceBg} />
      <ApplicationTools
        heading="Document Management Solutions designed for modern enterprises."
        items={firstData}
      />
      <CaseStudy
        tag="Success Stories"
        subheading={`Organizations using Telex document management solutions have experienced significant improvements in efficiency, collaboration, and document security. See how our platform can transform your document workflows.`}
        items={casestudyData}
      />
      <MonitorComponent
        heading="Seamless integration with your favorite tools"
        items={technologies}
      />
      <Guides />
      <Faq />
      <KeepTrack
        title="Transform your document management with Telex"
        content="Modernize your document creation and management processes with our intelligent platform. Experience the power of automated workflows, secure collaboration, and efficient document handling. Try Telex today."
      />
    </>
  );
};

export default DocumentManagement;
