"use server";
import React from "react";
import Faq from "~/telexComponents/homePageFaqs";
import Hero from "../components/single-product/Hero";
import ApplicationTools from "../components/single-product/ApplicationTools";
import CaseStudy from "../components/single-product/CaseStudies";
import ImageSection from "../components/single-product/ImageSection";
import MonitorComponent from "../components/single-product/Monitor";
import Guides from "../components/single-product/Guides";
import KeepTrack from "../components/single-product/KeepTrack";
import AppPerformanceBg from "../_assets/app-performance-bg.svg";
import CaseOne from "../_assets/case-image-one.svg";
import CaseTwo from "../_assets/case-image-two.svg";
import { technologies } from "../data/technologies";

const firstData = [
  {
    id: 1,
    title: "AI-Powered Sales Analysis",
    content: `Leverage advanced AI to analyze sales data, identify trends, and generate actionable insights. Enhance sales performance with intelligent predictions.`,
  },
  {
    id: 2,
    title: "Real-time Sales Monitoring",
    content: `Monitor sales performance 24/7 with AI-driven analytics. Get instant alerts and recommendations to optimize your sales strategy.`,
  },
  {
    id: 3,
    title: "Smart Lead Management",
    content: `Automatically qualify and prioritize leads using AI. Route high-potential opportunities to the right sales representatives for maximum conversion.`,
  },
  {
    id: 4,
    title: "Personalized Sales Strategies",
    content: `Develop data-driven sales approaches with AI that learns from successful deals and understands customer buying patterns.`,
  },
];

const casestudyData = [
  {
    id: 1,
    title: "How Telex AI increased sales revenue by 60%",
    image: CaseOne?.src,
    content: `A leading B2B company transformed their sales process with Telex AI, dramatically improving conversion rates and revenue generation.`,
  },
  {
    id: 2,
    title: "AI-driven sales optimization success",
    image: CaseTwo.src,
    content: `See how our AI sales system helped a tech company identify 2x more opportunities while reducing the sales cycle by 40%.`,
  },
  {
    id: 3,
    title: "Transforming sales performance with AI",
    image: CaseOne.src,
    content: `Learn how a retail company used Telex AI to optimize their sales strategy and increased average deal size by 35%.`,
  },
  {
    id: 4,
    title: "Scaling sales operations with AI",
    image: CaseTwo?.src,
    content: `Discover how a growing enterprise leveraged Telex AI to scale their sales operations and achieve record-breaking quarters.`,
  },
];

const SalesAI = () => {
  return (
    <>
      <Hero
        breadCumbs="AI-Powered Sales Analysis"
        title="Intelligent {{Sales Analysis}} with AI"
        content="Transform your sales performance with AI-powered analytics. Get actionable insights, predict trends, and optimize your sales strategy for maximum revenue growth."
        routeName="Sign up"
        learnMoreName="Learn more"
        routeLink="/"
        learnMoreLink="#"
      />
      <ImageSection image={AppPerformanceBg} />
      <ApplicationTools
        heading="AI-Powered Sales Solutions designed for modern businesses."
        items={firstData}
      />
      <CaseStudy
        tag="Success Stories"
        subheading={`Companies using Telex AI-powered sales analysis have seen significant improvements in revenue, conversion rates, and sales efficiency. See how our intelligent sales solutions can transform your sales performance.`}
        items={casestudyData}
      />
      <MonitorComponent
        heading="Comprehensive sales analysis across all channels"
        items={technologies}
      />
      <Guides />
      <Faq />
      <KeepTrack
        title="Elevate your sales performance with Telex AI-powered solutions"
        content="Transform your sales operations with intelligent analytics, real-time insights, and data-driven strategies. Try Telex today and see the difference AI can make in your sales success."
      />
    </>
  );
};

export default SalesAI;
