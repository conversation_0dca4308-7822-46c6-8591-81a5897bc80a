import React from "react";
import Faq from "~/telexComponents/homePageFaqs";
import Hero from "../components/single-product/Hero";
import ImageSection from "../components/single-product/ImageSection";
import ApplicationTools from "../components/single-product/ApplicationTools";
import CaseStudy from "../components/single-product/CaseStudies";
import MonitorComponent from "../components/single-product/Monitor";
import Guides from "../components/single-product/Guides";
import KeepTrack from "../components/single-product/KeepTrack";
import CaseOne from "../_assets/database1.svg";
import CaseTwo from "../_assets/database2.svg";
import DatabaseBg from "../_assets/database-bg.png";
import { databasetechnologies } from "../data/database-technologies";

const firstData = [
  {
    id: 1,
    title: "Monitor your database traffic with Telex",
    content: `Optimize app performance with real-time insights. Identify bottlenecks, improve speed, and enhance user experience.`,
  },
  {
    id: 2,
    title: "Uptime Monitoring with Telex",
    content: `Ensure your app is always available. Get instant alerts and
              notifications when downtime occurs, and minimize service
              disruptions.`,
  },
  {
    id: 3,
    title: "Telex helps Monitor User Activity",
    content: `Track user engagement. Monitor logins, sign-ups, and other key
              activities to understand user behavior and optimize the user
              experience.`,
  },
  {
    id: 4,
    title: "Error Tracking Easily with Telex",
    content: `Detect and fix errors quickly. Get real-time error tracking,
              alerts to resolve issues before they impact users.`,
  },
];

const casestudyData = [
  {
    id: 1,
    title: "How Telex can monitor database traffic",
    image: CaseOne?.src,
    content: `Limited visibility across systems force teams to constantly switch contexts, leaving problems unresolved and hindering the pace of innovation.`,
  },
  {
    id: 2,
    title: "Telex sustaining database efficiency",
    image: CaseTwo.src,
    content: `Fragmented observability tools reduce visibility, scalability, and reliability, negatively affecting uptime, delivery consistency, and overall performance.`,
  },
  {
    id: 3,
    title: "Monitoring your data in your database with telex",
    image: CaseOne?.src,
    content: `The high and hidden costs of managing disconnected and open-source tools can quickly accumulate, resulting in unexpected operational expenses.`,
  },
  {
    id: 4,
    title: "Telex sustaining database efficiency",
    image: CaseTwo.src,
    content: `Fragmented observability tools reduce visibility, scalability, and reliability, negatively affecting uptime, delivery consistency, and overall performance.`,
  },
];

const DatabaseMonitoring = () => {
  return (
    <>
      <Hero
        breadCumbs="Application Database Monitoring"
        title="Optimize Efficiency with Intelligent {{Database Monitoring}}"
        content="Real-time insights and proactive alerts to optimize your database performance and ensure smooth operations."
        routeName="Sign up"
        learnMoreName="Learn more"
        routeLink="#"
        learnMoreLink="#"
      />
      <ImageSection image={DatabaseBg?.src} />
      <ApplicationTools
        heading="Database Monitoring tool built for your databases"
        items={firstData}
      />
      <CaseStudy
        tag="Case Studies"
        subheading={`DevOps teams today rely on over 12 monitoring tools on average to
          gather and analyze the vast amounts of data generated across modern
          hybrid and cloud environments. Managing such a wide range of tools can
          strain your entire organization.`}
        items={casestudyData}
        background="#1B1B1B"
      />
      <MonitorComponent
        heading="Monitor your applications seamlessly, regardless of the technology."
        items={databasetechnologies}
      />
      <Guides />
      <Faq />
      <KeepTrack
        title="Keep track of your database’s events with Telex "
        content="Easily track queries and ensure smooth database operations, and simplify your management tasks."
      />
    </>
  );
};

export default DatabaseMonitoring;
